#!/bin/bash

# 启动 tmux 会话并正确配置 lerobot 环境
tmux new-session -d -s lerobot_session -c /root/workspace/lerobot

# 在 tmux 会话中设置环境
tmux send-keys -t lerobot_session 'conda activate lerobot' Enter
tmux send-keys -t lerobot_session 'export PATH="/root/miniconda3/envs/lerobot/bin:$PATH"' Enter
tmux send-keys -t lerobot_session 'alias python="/root/miniconda3/envs/lerobot/bin/python"' Enter
tmux send-keys -t lerobot_session 'alias pip="/root/miniconda3/envs/lerobot/bin/pip"' Enter
tmux send-keys -t lerobot_session 'echo "Python 版本: $(python --version)"' Enter
tmux send-keys -t lerobot_session 'echo "Python 路径: $(which python)"' Enter
tmux send-keys -t lerobot_session 'echo "lerobot 环境已正确配置！"' Enter

# 连接到会话
tmux attach-session -t lerobot_session
